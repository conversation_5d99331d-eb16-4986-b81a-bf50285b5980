import React from 'react';
import { BrowserRouter as Router } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import ErrorBoundary from './components/common/ErrorBoundary';
import { AppProvider } from './contexts/AppContext';
import { AuthProvider } from './hooks/useAuth';
import { AppRouter } from './AppRouter';
import './styles/app.css';

const App: React.FC = () => {
  console.log('🚀 App component is rendering!');

  return (
    <ErrorBoundary>
      <ConfigProvider
        locale={zhCN}
        theme={{
          token: {
            colorPrimary: '#2563eb',
            colorSuccess: '#10b981',
            colorWarning: '#f59e0b',
            colorError: '#ef4444',
            fontSize: 14,
            borderRadius: 8,
          },
        }}
      >
        <Router>
          <AuthProvider>
            <AppProvider>
              <AppRouter />
            </AppProvider>
          </AuthProvider>
        </Router>
      </ConfigProvider>
    </ErrorBoundary>
  );
};

export default App;